export interface ApplicationStatus {
    currentStatus: string;
    createdDate: string;
    lastUpdatedDate: string;
    notes: string;
}

export interface ApplicationDetails {
    education: string;
    workExperience: string;
    motivation: string;
}

export interface CertificationData {
    description: string;
}

export interface NewApplicationData {
    education: string;
    workExperience: string;
    motivation: string;
    certifications: CertificationData[];
    uploadFileName: string;
}

export interface MentorApplicationCredentials {
    email: string;
    password: string;
    fullName: string;
    applicationStatus: ApplicationStatus;
    applicationDetails: ApplicationDetails;
    supportingDocuments: string;
}

export interface NewMentorCredentials {
    email: string;
    password: string;
    fullName: string;
    newApplicationData: NewApplicationData;
    expectedApplicationStatus: ApplicationStatus;
    expectedApplicationDetails: ApplicationDetails;
    expectedSupportingDocuments: string;
}

export interface MentorApplicationTrackingTestData {
    presubmittedApplicationMentor: MentorApplicationCredentials;
    newMentor: NewMentorCredentials;
}
