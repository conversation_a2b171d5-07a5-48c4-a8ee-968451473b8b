export interface ApplicationStatus {
    currentStatus: string;
    createdDate: string;
    lastUpdatedDate: string;
    notes: string;
}

export interface ApplicationDetails {
    education: string;
    workExperience: string;
    motivation: string;
}

export interface MentorApplicationCredentials {
    email: string;
    password: string;
    fullName: string;
    applicationStatus: ApplicationStatus;
    applicationDetails: ApplicationDetails;
    supportingDocuments: string;
}

export interface ApplicationData {
    education: string;
    workExperience: string;
    motivation: string;
    certification: string;
}

export interface ExpectedApplicationStatus {
    currentStatus: string;
    notes: string;
}

export interface ExpectedApplicationDetails {
    education: string;
    workExperience: string;
    motivation: string;
}

export interface NewMentorForApplication {
    email: string;
    password: string;
    fullName: string;
    applicationData: ApplicationData;
    expectedApplicationStatus: ExpectedApplicationStatus;
    expectedApplicationDetails: ExpectedApplicationDetails;
    expectedSupportingDocuments: string;
}

export interface MentorApplicationTrackingTestData {
    presubmittedApplicationMentor: MentorApplicationCredentials;
    newMentorForApplication: NewMentorForApplication;
}
