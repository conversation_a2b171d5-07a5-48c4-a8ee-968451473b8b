import { Locator, Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';

export class MentorApplicationStatusPage extends BasePage {
    // Main page locators
    private readonly mentorApplicationStatusHeading: Locator;
    private readonly refreshButton: Locator;

    // Page field locators
    private readonly applicationStatusField: Locator;
    private readonly applicationDetailsField: Locator;
    private readonly supportingDocumentsField: Locator;

    constructor(page: Page) {
        super(page);
        this.mentorApplicationStatusHeading = this.page.getByText('Mentor Application Status');
        this.refreshButton = this.page.getByRole('button', { name: 'Refresh' });

        // Page field locators
        this.applicationStatusField = this.page.getByText('Application Status', { exact: true });
        this.applicationDetailsField = this.page.getByText('Application Details');
        this.supportingDocumentsField = this.page.getByText('Supporting Documents', { exact: true });
    }

    async verifyOnMentorApplicationStatusPage(): Promise<void> {
        await expect(this.mentorApplicationStatusHeading).toBeVisible();
    }

    async clickMentorApplicationStatusHeading(): Promise<void> {
        await this.mentorApplicationStatusHeading.click();
    }

    async clickRefreshButton(): Promise<void> {
        await this.refreshButton.click();
    }

    async verifyMentorFullName(expectedName: string): Promise<void> {
        const nameHeading = this.page.getByRole('heading', { name: expectedName });
        await expect(nameHeading).toBeVisible();
        await nameHeading.click();
    }

    async verifyMentorEmail(expectedEmail: string): Promise<void> {
        const emailText = this.page.getByText(expectedEmail);
        await expect(emailText).toBeVisible();
        await emailText.click();
    }

    async verifyMentorDataAfterRefresh(fullName: string, email: string): Promise<void> {
        await this.verifyMentorFullName(fullName);
        await this.verifyMentorEmail(email);
    }

    async verifyApplicationStatusField(): Promise<void> {
        await expect(this.applicationStatusField).toBeVisible();
        await this.applicationStatusField.click();
    }

    async verifyApplicationDetailsField(): Promise<void> {
        await expect(this.applicationDetailsField).toBeVisible();
        await this.applicationDetailsField.click();
    }

    async verifySupportingDocumentsField(): Promise<void> {
        await expect(this.supportingDocumentsField).toBeVisible();
        await this.supportingDocumentsField.click();
    }

    async verifyAllPageFields(): Promise<void> {
        await this.verifyApplicationStatusField();
        await this.verifyApplicationDetailsField();
        await this.verifySupportingDocumentsField();
    }

    // Detailed status verification methods
    async verifyCurrentStatus(expectedStatus: string): Promise<void> {
        await this.page.getByText('Current Status').click();
        const statusElement = this.page.getByText(expectedStatus, { exact: true });
        await expect(statusElement).toBeVisible();
        await statusElement.click();
    }

    async verifyCreatedDate(expectedDate: string): Promise<void> {
        await this.page.getByText('Created on:').click();
        const dateElement = this.page.getByText(expectedDate).first();
        await expect(dateElement).toBeVisible();
        await dateElement.click();
    }

    async verifyLastUpdatedDate(expectedDate: string): Promise<void> {
        await this.page.getByText('Last updated:').click();
        const dateElement = this.page.getByText(expectedDate).nth(1);
        await expect(dateElement).toBeVisible();
        await dateElement.click();
    }

    async verifyNotes(expectedNotes: string): Promise<void> {
        await this.page.getByText('Notes', { exact: true }).click();
        const notesElement = this.page.getByText(expectedNotes);
        await expect(notesElement).toBeVisible();
        await notesElement.click();
    }

    // Application details verification methods
    async verifyEducationSection(expectedEducation: string): Promise<void> {
        const educationHeading = this.page.getByRole('heading', { name: 'Education' });
        await expect(educationHeading).toBeVisible();
        await educationHeading.click();

        const educationContent = this.page.getByText(expectedEducation);
        await expect(educationContent).toBeVisible();
    }

    async verifyWorkExperienceSection(expectedWorkExperience: string): Promise<void> {
        const workExperienceHeading = this.page.getByRole('heading', { name: 'Work Experience' });
        await expect(workExperienceHeading).toBeVisible();
        await workExperienceHeading.click();

        const workExperienceContent = this.page.getByText(expectedWorkExperience);
        await expect(workExperienceContent).toBeVisible();
    }

    async verifyMotivationSection(expectedMotivation: string): Promise<void> {
        const motivationHeading = this.page.getByRole('heading', { name: 'Motivation' });
        await expect(motivationHeading).toBeVisible();
        await motivationHeading.click();

        const motivationContent = this.page.getByText(expectedMotivation);
        await expect(motivationContent).toBeVisible();
    }

    async verifySupportingDocumentsContent(expectedDocuments: string): Promise<void> {
        const documentsElement = this.page.getByText(expectedDocuments);
        await expect(documentsElement).toBeVisible();
        await documentsElement.click();
    }
}
