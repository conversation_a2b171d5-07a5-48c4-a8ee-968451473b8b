import { test, expect } from '@playwright/test';
import { LoginPage } from '../../../pages/login/login-page';
import { MentorApplicationStatusPage } from '../../../pages/mentor-application/mentor-application-status-page';
import testData from '../../../tests-data/mentor-application-tracking-data.json';
import { MentorApplicationTrackingTestData } from '../../../data-type/mentor-application-tracking.type';

// Configure test to run on Chromium only
test.describe('Mentor Application Submission Tests', () => {
    test.describe.configure({ mode: 'parallel' });

    let loginPage: LoginPage;
    let mentorApplicationStatusPage: MentorApplicationStatusPage;
    const trackingData: MentorApplicationTrackingTestData = testData as MentorApplicationTrackingTestData;

    test.beforeEach(async ({ page, browserName }) => {
        // Skip test if not running on Chromium
        test.skip(browserName !== 'chromium', 'This test only runs on Chromium');

        loginPage = new LoginPage(page);
        mentorApplicationStatusPage = new MentorApplicationStatusPage(page);
    });

    test('@MentorApplicationSubmission Create new application successfully test', async ({ page }) => {
        const newMentorCredentials = trackingData.newMentorForApplication;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Login as new mentor user from test-data', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                newMentorCredentials.email,
                newMentorCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Step 4: Make sure it\'s on Mentor Application Status page', async () => {
            await mentorApplicationStatusPage.verifyOnMentorApplicationStatusPage();
            await mentorApplicationStatusPage.clickMentorApplicationStatusHeading();
        });

        await test.step('Step 5: Click Create Application button', async () => {
            await mentorApplicationStatusPage.clickCreateApplicationButton();
        });

        await test.step('Check result: Check fullname and email make sure match account from test-data', async () => {
            await mentorApplicationStatusPage.verifyMentorNameAndEmailOnForm(
                newMentorCredentials.fullName,
                newMentorCredentials.email
            );
        });

        await test.step('Fill data into fields from test-data', async () => {
            await mentorApplicationStatusPage.fillApplicationForm(
                newMentorCredentials.applicationData.education,
                newMentorCredentials.applicationData.workExperience,
                newMentorCredentials.applicationData.motivation,
                newMentorCredentials.applicationData.certification,
                'images.jpg'
            );
        });

        await test.step('Verify application status is pending and check timestamps and notes', async () => {
            await mentorApplicationStatusPage.verifyCurrentStatus(newMentorCredentials.expectedApplicationStatus.currentStatus);
            await mentorApplicationStatusPage.verifyNotes(newMentorCredentials.expectedApplicationStatus.notes);
            
            // Verify timestamps exist (we can't predict exact values)
            await mentorApplicationStatusPage.page.getByText('Created on:').click();
            await mentorApplicationStatusPage.page.getByText('Last updated:').click();
        });

        await test.step('Check application details sections', async () => {
            await mentorApplicationStatusPage.verifyApplicationDetailsField();
            await mentorApplicationStatusPage.verifyEducationSection(newMentorCredentials.expectedApplicationDetails.education);
            await mentorApplicationStatusPage.verifyWorkExperienceSection(newMentorCredentials.expectedApplicationDetails.workExperience);
            await mentorApplicationStatusPage.verifyMotivationSection(newMentorCredentials.expectedApplicationDetails.motivation);
        });

        await test.step('Check supporting documents section', async () => {
            await mentorApplicationStatusPage.verifySupportingDocumentsField();
            await mentorApplicationStatusPage.verifySupportingDocumentsContent(newMentorCredentials.expectedSupportingDocuments);
        });
    });
});
