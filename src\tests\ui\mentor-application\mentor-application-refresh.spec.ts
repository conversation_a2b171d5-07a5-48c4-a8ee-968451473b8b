import { test, expect } from '@playwright/test';
import { LoginPage } from '../../../pages/login/login-page';
import { MentorApplicationStatusPage } from '../../../pages/mentor-application/mentor-application-status-page';
import testData from '../../../tests-data/mentor-application-tracking-data.json';
import { MentorApplicationTrackingTestData } from '../../../data-type/mentor-application-tracking.type';

// Configure test to run on Chromium only
test.describe('Mentor Application Tracking Tests', () => {
    test.describe.configure({ mode: 'parallel' });

    let loginPage: LoginPage;
    let mentorApplicationStatusPage: MentorApplicationStatusPage;
    const trackingData: MentorApplicationTrackingTestData = testData as MentorApplicationTrackingTestData;

    test.beforeEach(async ({ page, browserName }) => {
        // Skip test if not running on Chromium
        test.skip(browserName !== 'chromium', 'This test only runs on Chromium');

        loginPage = new LoginPage(page);
        mentorApplicationStatusPage = new MentorApplicationStatusPage(page);
    });

    test('@MentorApplicationTracking Refresh test - Verify mentor data after refresh', async ({ page }) => {
        const mentorCredentials = trackingData.presubmittedApplicationMentor;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Click sign in, input email password, click sign in - login as presubmitted application mentor from test-data', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                mentorCredentials.email,
                mentorCredentials.password
            );
            await loginPage.clickOnSignInButton();

            // Verify login success by checking for 'Mentor Platform' link
            await page.waitForLoadState('networkidle');
            const mentorPlatformLink = page.getByRole('link', { name: 'Mentor Platform' });
            await expect(mentorPlatformLink).toBeVisible();
        });

        await test.step('Step 4: Make sure it\'s on Mentor Application Status page', async () => {
            await mentorApplicationStatusPage.verifyOnMentorApplicationStatusPage();
            await mentorApplicationStatusPage.clickMentorApplicationStatusHeading();
        });

        await test.step('Step 5: Click refresh button', async () => {
            await mentorApplicationStatusPage.clickRefreshButton();
        });

        await test.step('Check result: Check fullname and email make sure match account from test-data', async () => {
            await mentorApplicationStatusPage.verifyMentorDataAfterRefresh(
                mentorCredentials.fullName,
                mentorCredentials.email
            );
        });
    });

    test('@MentorApplicationTracking Check page field test - Verify all page fields load correctly', async ({ page }) => {
        const mentorCredentials = trackingData.presubmittedApplicationMentor;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Click sign in, input email password, click sign in - login as presubmitted application mentor from test-data', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                mentorCredentials.email,
                mentorCredentials.password
            );
            await loginPage.clickOnSignInButton();

            // Verify login success by checking for 'Mentor Platform' link
            await page.waitForLoadState('networkidle');
            const mentorPlatformLink = page.getByRole('link', { name: 'Mentor Platform' });
            await expect(mentorPlatformLink).toBeVisible();
        });

        await test.step('Step 4: Make sure it\'s on Mentor Application Status page', async () => {
            await mentorApplicationStatusPage.verifyOnMentorApplicationStatusPage();
            await mentorApplicationStatusPage.clickMentorApplicationStatusHeading();
        });

        await test.step('Step 5: Check result - Check fullname and email make sure match account from test-data', async () => {
            await mentorApplicationStatusPage.verifyMentorDataAfterRefresh(
                mentorCredentials.fullName,
                mentorCredentials.email
            );
        });

        await test.step('Step 6: Check all field make sure web load ok', async () => {
            await mentorApplicationStatusPage.verifyAllPageFields();
        });
    });

    test('@MentorApplicationTracking View Functions test - Verify all detailed information', async ({ page }) => {
        const mentorCredentials = trackingData.presubmittedApplicationMentor;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Click sign in, input email password, click sign in - login as presubmitted application mentor from test-data', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                mentorCredentials.email,
                mentorCredentials.password
            );
            await loginPage.clickOnSignInButton();

            // Verify login success by checking for 'Mentor Platform' link
            await page.waitForLoadState('networkidle');
            const mentorPlatformLink = page.getByRole('link', { name: 'Mentor Platform' });
            await expect(mentorPlatformLink).toBeVisible();
        });

        await test.step('Step 4: Make sure it\'s on Mentor Application Status page', async () => {
            await mentorApplicationStatusPage.verifyOnMentorApplicationStatusPage();
            await mentorApplicationStatusPage.clickMentorApplicationStatusHeading();
        });

        await test.step('Step 5: Check result - Check fullname and email make sure match account from test-data', async () => {
            await mentorApplicationStatusPage.verifyMentorDataAfterRefresh(
                mentorCredentials.fullName,
                mentorCredentials.email
            );

            // Check status is pending and check time, also check there is no note
            await mentorApplicationStatusPage.verifyCurrentStatus(mentorCredentials.applicationStatus.currentStatus);
            await mentorApplicationStatusPage.verifyCreatedDate(mentorCredentials.applicationStatus.createdDate);
            await mentorApplicationStatusPage.verifyLastUpdatedDate(mentorCredentials.applicationStatus.lastUpdatedDate);
            await mentorApplicationStatusPage.verifyNotes(mentorCredentials.applicationStatus.notes);

            // Check application details
            await mentorApplicationStatusPage.verifyApplicationDetailsField();

            // Check education field
            await mentorApplicationStatusPage.verifyEducationSection(mentorCredentials.applicationDetails.education);

            // Check work experience
            await mentorApplicationStatusPage.verifyWorkExperienceSection(mentorCredentials.applicationDetails.workExperience);

            // Check motivation
            await mentorApplicationStatusPage.verifyMotivationSection(mentorCredentials.applicationDetails.motivation);

            // Check supporting documents
            await mentorApplicationStatusPage.verifySupportingDocumentsField();
            await mentorApplicationStatusPage.verifySupportingDocumentsContent(mentorCredentials.supportingDocuments);
        });
    });

    test('@MentorApplicationTracking Create new application successfully test - Verify new mentor can create application', async ({ page }) => {
        const newMentorCredentials = trackingData.newMentor;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Click sign in, input email password, click sign in - login as new mentor user from test-data', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                newMentorCredentials.email,
                newMentorCredentials.password
            );
            await loginPage.clickOnSignInButton();

            // Verify login success by checking for 'Mentor Platform' link
            await page.waitForLoadState('networkidle');
            const mentorPlatformLink = page.getByRole('link', { name: 'Mentor Platform' });
            await expect(mentorPlatformLink).toBeVisible();
        });

        await test.step('Step 4: Make sure it\'s on Mentor Application Status page', async () => {
            await mentorApplicationStatusPage.verifyOnMentorApplicationStatusPage();
            await mentorApplicationStatusPage.clickMentorApplicationStatusHeading();
        });

        await test.step('Step 5: Click Create Application button', async () => {
            await mentorApplicationStatusPage.clickCreateApplicationButton();
        });

        await test.step('Check result: Check fullname and email make sure match account from test-data', async () => {
            await mentorApplicationStatusPage.verifyMentorNameAndEmailOnForm(
                newMentorCredentials.fullName,
                newMentorCredentials.email
            );
        });

        await test.step('Fill application form with data from test-data and submit', async () => {
            await mentorApplicationStatusPage.fillCompleteApplicationForm(newMentorCredentials.newApplicationData);
        });

        await test.step('Verify application status after submission', async () => {
            // Check status is pending and check time, also check there is no note
            await mentorApplicationStatusPage.verifyCurrentStatus(newMentorCredentials.expectedApplicationStatus.currentStatus);
            await mentorApplicationStatusPage.verifyCreatedDate(newMentorCredentials.expectedApplicationStatus.createdDate);
            await mentorApplicationStatusPage.verifyLastUpdatedDate(newMentorCredentials.expectedApplicationStatus.lastUpdatedDate);
            await mentorApplicationStatusPage.verifyNotes(newMentorCredentials.expectedApplicationStatus.notes);
        });

        await test.step('Verify application details after submission', async () => {
            // Check application details
            await mentorApplicationStatusPage.verifyApplicationDetailsField();

            // Check education field
            await mentorApplicationStatusPage.verifyEducationSection(newMentorCredentials.expectedApplicationDetails.education);

            // Check work experience
            await mentorApplicationStatusPage.verifyWorkExperienceSection(newMentorCredentials.expectedApplicationDetails.workExperience);

            // Check motivation
            await mentorApplicationStatusPage.verifyMotivationSection(newMentorCredentials.expectedApplicationDetails.motivation);

            // Check supporting documents
            await mentorApplicationStatusPage.verifySupportingDocumentsField();
            await mentorApplicationStatusPage.verifySupportingDocumentsContent(newMentorCredentials.expectedSupportingDocuments);
        });
    });
});
